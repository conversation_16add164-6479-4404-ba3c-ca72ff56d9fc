package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存解析图片结果请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-07-28
 */
@Data
@ApiModel("保存解析图片结果请求")
public class SaveParsedFiguresRequestDTO {

    /**
     * 图片S3键列表
     */
    @ApiModelProperty(value = "图片S3键列表", required = true, example = "[\"folder/image1.png\", \"folder/image2.jpg\"]")
    @JsonProperty("image_s3_keys")
    @NotEmpty(message = "图片S3键列表不能为空")
    private List<String> imageS3Keys;
}
