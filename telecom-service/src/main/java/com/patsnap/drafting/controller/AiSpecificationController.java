package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.aispecification.SpecificationFileUploadManager;
import com.patsnap.drafting.manager.aispecification.SpecificationManager;
import com.patsnap.drafting.manager.aispecification.content.strategy.SpecificationContentManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentOutlineBO;
import com.patsnap.drafting.model.aispecification.TermContentBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aispecification.ClaimFormattedResDTO;
import com.patsnap.drafting.request.aispecification.ClassificationHelperResponse;
import com.patsnap.drafting.request.aispecification.ClassificationSubClassResponse;
import com.patsnap.drafting.request.aispecification.ContentModifyReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentAddReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentGenerateReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentKeyWordReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentKeyWordResDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentOutlineModifyReqDTO;
import com.patsnap.drafting.request.aispecification.InputContentResDTO;
import com.patsnap.drafting.request.aispecification.SaveParsedFiguresRequestDTO;
import com.patsnap.drafting.request.aispecification.SingleEmbodimentModifyReqDTO;
import com.patsnap.drafting.request.aispecification.SpecificationContentReqDTO;
import com.patsnap.drafting.request.aispecification.SpecificationInitDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.imagesearch.ImageUploadRequestDTO;
import com.patsnap.drafting.response.DocumentParseResponseDTO;
import com.patsnap.drafting.response.aispecification.SpecificationClassificationResDTO;
import com.patsnap.drafting.response.aispecification.SpecificationLangCheckResDTO;
import com.patsnap.drafting.response.imagesearch.ImageUploadResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Api(tags = "AI说明书")
@RestController
@RequestMapping("/ai-specification")
@RequiredArgsConstructor
@Slf4j
public class AiSpecificationController {

    private final SpecificationManager specificationManager;

    private final SpecificationContentManager specificationContentManager;
    
    private final AiTaskManager aitaskManager;
    
    private final SpecificationFileUploadManager specificationFileUploadManager;


    @ApiOperation("语言检测")
    @PostMapping("/lang-detect")
    public CommonResponse<SpecificationLangCheckResDTO> langDetect(
            @Valid @NotNull @RequestBody SpecificationInitDTO specificationInitDTO) {
        return CommonResponse.<SpecificationLangCheckResDTO>builder()
                .withData(specificationManager.langDetect(specificationInitDTO)).build();
    }

    @ApiOperation("任务开始")
    @PostMapping("/task/init")
    public CommonResponse<Void> taskInit(@Valid @NotNull @RequestBody SpecificationInitDTO specificationInitDTO) {
        specificationManager.taskInit(specificationInitDTO);
        return CommonResponse.<Void>builder().build();
    }

    @ApiOperation("获取用户输入内容")
    @PostMapping("/input-content")
    public CommonResponse<InputContentResDTO> getInputContent(
            @Valid @NotNull @RequestBody SpecificationContentReqDTO specificationContentReqDTO) {
        return CommonResponse.<InputContentResDTO>builder()
                .withData(specificationManager.getInputContent(specificationContentReqDTO)).build();
    }


    @ApiOperation("获取分类号")
    @PostMapping("/classification")
    public CommonResponse<SpecificationClassificationResDTO> getClassification(
            @Valid @NotNull @RequestBody SpecificationContentReqDTO contentDTO) {
        return CommonResponse.<SpecificationClassificationResDTO>builder()
                .withData(new SpecificationClassificationResDTO(specificationManager.getClassification(contentDTO)))
                .build();
    }


    @GetMapping("/classification/helper-section")
    public CommonResponse<ClassificationSubClassResponse> helperSection(@NotBlank @RequestParam String type) {
        return specificationManager.helperSection(type);
    }

    @GetMapping("/classification/helper-subclass")
    public CommonResponse<ClassificationSubClassResponse> helperSubClass(@NotBlank @RequestParam String type,
            @NotBlank @RequestParam String code,
            @RequestParam(required = false, defaultValue = "0") Integer start, @RequestParam int level) {
        return specificationManager.helperSubClass(type.toLowerCase(), code.trim(), level, start);
    }

    @GetMapping("/classification/helper-search")
    public CommonResponse<ClassificationHelperResponse> helperSearch(@NotBlank @RequestParam String type,
            @NotBlank @RequestParam String q, @NotBlank @RequestParam(required = false, defaultValue = "0") int start) {
        return specificationManager.helperSearch(type, q, start);
    }


    @ApiOperation("获取AI返回的结果")
    @PostMapping(value = "/content", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux getAiContent(@Valid @NotNull @RequestBody GenerateContentRequestDTO contentRequestDTO) {
        return specificationContentManager.getAiContent(contentRequestDTO);
    }

    @ApiOperation("调整AI返回的结果")
    @PostMapping(value = "/content/{operation}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux modifyAiContent(@Valid @NotNull @RequestBody GenerateContentRequestDTO contentRequestDTO,
            @PathVariable String operation) {
        return specificationContentManager.modifyContent(contentRequestDTO, operation);
    }

    @ApiOperation("修改AI说明书内容，包括三要素、类别、分类号、附图信息、术语信息等")
    @PutMapping("/content/modify")
    public CommonResponse<Void> modifyAiContent(@Valid @NotNull @RequestBody ContentModifyReqDTO contentRequestDTO) {
        specificationManager.contentModify(contentRequestDTO);
        return CommonResponse.<Void>builder().build();
    }


    @ApiOperation("格式化权利要求")
    @PostMapping("/claim/format")
    public CommonResponse<ClaimFormattedResDTO> getClaimFormatted(
            @Valid @NotNull @RequestBody SpecificationContentReqDTO specificationContentReqDTO) {
        return CommonResponse.<ClaimFormattedResDTO>builder()
                .withData(new ClaimFormattedResDTO(specificationManager.claimFormat(specificationContentReqDTO)))
                .build();
    }

    @ApiOperation("实施例-生成扩展词")
    @PostMapping("/embodiment/keywords")
    public CommonResponse<EmbodimentKeyWordResDTO> keywordsExpand(
            @Valid @NotNull @RequestBody EmbodimentKeyWordReqDTO embodimentKeyWordReqDTO) {
        return CommonResponse.<EmbodimentKeyWordResDTO>builder()
                .withData(specificationManager.embodimentKeyword(embodimentKeyWordReqDTO))
                .build();
    }


    @ApiOperation("实施例-生成实施例")
    @PostMapping(value = "/embodiment/add", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux addEmbodiment(
            @Valid @NotNull @RequestBody EmbodimentAddReqDTO embodimentAddReqDTO) {
        aitaskManager.checkEditPermission(embodimentAddReqDTO.getTaskId());
        return specificationContentManager.getAiContent(embodimentAddReqDTO);
    }

    @ApiOperation("批量上传图片")
    @PostMapping("task/{task_id}/image-upload")
    public CommonResponse<ImageUploadResponseDTO> uploadMultipleImages(
            @ApiParam(value = "图片文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            @ApiParam(value = "任务ID")
            @PathVariable(value = "task_id") String taskId) {

        log.info("接收到批量图片上传请求，文件数量: {}", files != null ? files.length : 0);

        // 调用业务逻辑处理上传
        ImageUploadResponseDTO result = specificationManager.uploadMultipleImages(taskId, files, new ImageUploadRequestDTO());

        log.info("批量图片上传完成，总数: {}, 成功: {}, 失败: {}",
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return CommonResponse.<ImageUploadResponseDTO>builder().withData(result).build();
    }

    @ApiOperation("获取附图信息")
    @GetMapping("task/{task_id}/content/figures")
    public CommonResponse<FigureContentBO> getFigureInfo(@ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<FigureContentBO>builder()
                .withData(specificationManager.getFigureInfo(taskId))
                .build();
    }

    @ApiOperation("获取术语信息")
    @GetMapping("task/{task_id}/content/terms")
    public CommonResponse<TermContentBO> getTermInfo(@ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<TermContentBO>builder()
                .withData(specificationManager.getTermInfo(taskId))
                .build();
    }

    @ApiOperation("获取算法处理状态")
    @GetMapping("task/{task_id}/algorithm-status")
    public CommonResponse<Map<String, String>> getAlgorithmStatus(
            @ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<Map<String, String>>builder()
                .withData(specificationManager.getAlgorithmStatus(taskId))
                .build();
    }

    @ApiOperation("实施例-生成实施例大纲")
    @PostMapping("/embodiment/outline")
    public CommonResponse<SpecificationEmbodimentOutlineBO> generateEmbodimentOutline(
            @Valid @NotNull @RequestBody AiTaskReqDTO aiTaskReqDTO) {
        return CommonResponse.<SpecificationEmbodimentOutlineBO>builder()
                .withData(specificationManager.generateEmbodimentOutline(aiTaskReqDTO))
                .build();
    }

    @ApiOperation("实施例-新增/修改实施例大纲")
    @PostMapping("/embodiment/outline/modify")
    public CommonResponse<SpecificationEmbodimentOutlineBO> modifyEmbodimentOutline(
            @Valid @NotNull @RequestBody EmbodimentOutlineModifyReqDTO embodimentOutlineModifyReqDTO) {
        return CommonResponse.<SpecificationEmbodimentOutlineBO>builder()
                .withData(specificationManager.modifyEmbodimentOutline(embodimentOutlineModifyReqDTO))
                .build();
    }

    @ApiOperation("实施例-生成单个实施例")
    @PostMapping(value = "/embodiment/content", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux generateSingleEmbodiment(@Valid @NotNull @RequestBody EmbodimentGenerateReqDTO embodimentGenerateReqDTO) {
        return specificationContentManager.getAiContent(embodimentGenerateReqDTO);
    }

    @ApiOperation("实施例-保存单个实施例")
    @PutMapping(value = "/embodiment/content", produces = MediaType.APPLICATION_JSON_VALUE)
    public CommonResponse<Void> saveSingleEmbodiment(@Valid @NotNull @RequestBody SingleEmbodimentModifyReqDTO singleEmbodimentModifyReqDTO) {
        specificationManager.saveSingleEmbodiment(singleEmbodimentModifyReqDTO);
        return CommonResponse.<Void>builder().build();
    }

    @ApiOperation("实施例-删除单个实施例")
    @DeleteMapping(value = "/embodiment/content", produces = MediaType.APPLICATION_JSON_VALUE)
    public CommonResponse<Void> deleteSingleEmbodiment(@Valid @NotNull @RequestBody SingleEmbodimentModifyReqDTO singleEmbodimentModifyReqDTO) {
        specificationManager.deleteSingleEmbodiment(singleEmbodimentModifyReqDTO);
        return CommonResponse.<Void>builder().build();
    }
    
    @ApiOperation("上传文件")
    @PostMapping("task/{task_id}/file-upload")
    public CommonResponse<Boolean> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<Boolean>builder()
                .withData(specificationFileUploadManager.uploadFile(taskId, file))
                .build();
    }
    
    @ApiOperation("文件解析")
    @PostMapping("task/{task_id}/file-parse")
    public CommonResponse<DocumentParseResponseDTO> parseFile(
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "类型(DISCLOSURE or CLAIM)", required = true) @RequestParam("content_type") String contentType,
            @ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<DocumentParseResponseDTO>builder()
                .withData(specificationFileUploadManager.parseFile(taskId, file, contentType))
                .build();
    }
    
    @ApiOperation("获取解析的图片结果")
    @GetMapping("task/{task_id}/parse-images")
    public CommonResponse<DocumentParseResponseDTO> getParseImages (
            @ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<DocumentParseResponseDTO>builder()
                .withData(specificationFileUploadManager.getParseImages(taskId))
                .build();
    }

    @ApiOperation("保存解析图片结果到附图列表")
    @PostMapping("task/{task_id}/parsed-figures")
    public CommonResponse<FigureContentBO> saveParsedFigures(
            @ApiParam(value = "任务ID") @PathVariable(value = "task_id") String taskId,
            @Valid @NotNull @RequestBody SaveParsedFiguresRequestDTO request) {
        return CommonResponse.<FigureContentBO>builder()
                .withData(specificationFileUploadManager.saveParsedFigures(taskId, request.getImageS3Keys()))
                .build();
    }

}
