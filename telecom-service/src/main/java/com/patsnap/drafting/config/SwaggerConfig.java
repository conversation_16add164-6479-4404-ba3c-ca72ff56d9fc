package com.patsnap.drafting.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.Contact;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Swagger API 文档配置
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-28
 */
@Configuration
@EnableSwagger2
@ConditionalOnProperty(name = "configs.com.patsnap.swagger-ui.enabled", havingValue = "true", matchIfMissing = false)
public class SwaggerConfig {

    @Value("${spring.application.name:s-analytics-telecom}")
    private String applicationName;

    @Value("${server.servlet.context-path:/telecom}")
    private String contextPath;

    /**
     * 创建 API 文档的 Docket Bean
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                // 扫描指定包下的 Controller
                .apis(RequestHandlerSelectors.basePackage("com.patsnap.drafting.controller"))
                // 扫描所有路径
                .paths(PathSelectors.any())
                .build()
                // 添加安全配置
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }

    /**
     * API 基本信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("PatSnap Analytics Telecom API")
                .description("PatSnap 专利分析与起草服务 API 文档")
                .version("1.0.0")
                .contact(new Contact(
                        "PatSnap Development Team",
                        "https://www.patsnap.com",
                        "<EMAIL>"
                ))
                .license("PatSnap License")
                .licenseUrl("https://www.patsnap.com/license")
                .build();
    }

    /**
     * 安全配置方案
     */
    private List<SecurityScheme> securitySchemes() {
        return Arrays.asList(
                new ApiKey("X-User-ID", "X-User-ID", "header"),
                new ApiKey("X-PatSnap-From", "X-PatSnap-From", "header"),
                new ApiKey("Authorization", "Authorization", "header")
        );
    }

    /**
     * 安全上下文配置
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(defaultAuth())
                        .forPaths(PathSelectors.regex("^(?!.*(/health|/ping|/error|/manage)).*$"))
                        .build()
        );
    }

    /**
     * 默认的安全引用
     */
    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        
        return Arrays.asList(
                new SecurityReference("X-User-ID", authorizationScopes),
                new SecurityReference("X-PatSnap-From", authorizationScopes),
                new SecurityReference("Authorization", authorizationScopes)
        );
    }
}
