package com.patsnap.drafting.manager;

import com.patsnap.drafting.enums.DocumentParseTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.DocumentParseErrorCodeEnum;
import com.patsnap.drafting.request.DocumentParseRequestDTO;
import com.patsnap.drafting.response.DocumentParseResponseDTO;
import com.patsnap.drafting.util.DocumentParseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 文档解析业务管理器
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentParseManager {

    private final FileManager fileManager;
    
    public static final String DRAFTING_SPECIFICATION_DOCUMENT_PARSE_DOC_IMAGE = "drafting/specification/document_parse/doc_image_";
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final List<String> SUPPORTED_EXTENSIONS = List.of(".pdf", ".doc", ".docx");

    /**
     * 解析文档
     *
     * @param file 上传的文件
     * @param request 解析请求参数
     * @return 解析结果
     */
    public DocumentParseResponseDTO parseDocument(MultipartFile file, DocumentParseRequestDTO request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证文件
            validateFile(file);
            
            // 获取解析类型
            DocumentParseTypeEnum parseType = DocumentParseTypeEnum.fromCode(request.getParseType());
            
            log.info("开始解析文档，文件名: {}, 解析类型: {}, 文件大小: {} bytes", 
                    file.getOriginalFilename(), parseType.getDescription(), file.getSize());

            // 解析文档
            DocumentParseUtils.DocumentParseResult parseResult = DocumentParseUtils.parseDocument(file, parseType);
            
            // 构建响应
            DocumentParseResponseDTO response = buildResponse(parseResult);
            
            // 如果需要解析图片，上传图片到S3
            if (parseType.needParseImage() && CollectionUtils.isNotEmpty(parseResult.getImages())) {
                uploadImagesToS3(parseResult.getImages(), response, request);
            }
            
            response.setStatus("SUCCESS");
            response.setProcessingTime(System.currentTimeMillis() - startTime);
            
            log.info("文档解析完成，文件名: {}, 耗时: {} ms", file.getOriginalFilename(), response.getProcessingTime());
            
            return response;
            
        } catch (Exception e) {
            log.error("文档解析失败，文件名: {}", file.getOriginalFilename(), e);
            
            return DocumentParseResponseDTO.builder()
                    .originalFileName(file.getOriginalFilename())
                    .fileSize(file.getSize())
                    .parseType(request.getParseType())
                    .status("FAILED")
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    /**
     * 验证文件
     */
    public void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BizException(DocumentParseErrorCodeEnum.FILE_EMPTY);
        }

        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new BizException(DocumentParseErrorCodeEnum.FILE_NAME_INVALID);
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BizException(DocumentParseErrorCodeEnum.FILE_SIZE_EXCEEDED);
        }

        // 检查文件格式
        String lowerFileName = originalFilename.toLowerCase();
        boolean supportedFormat = SUPPORTED_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);

        if (!supportedFormat) {
            throw new BizException(DocumentParseErrorCodeEnum.FILE_FORMAT_NOT_SUPPORTED);
        }

        log.info("文件验证通过，文件名: {}, 大小: {} bytes", originalFilename, file.getSize());
    }

    /**
     * 构建响应对象
     */
    private DocumentParseResponseDTO buildResponse(DocumentParseUtils.DocumentParseResult parseResult) {
        return DocumentParseResponseDTO.builder()
                .parseType(parseResult.getParseType())
                .originalFileName(parseResult.getOriginalFileName())
                .fileSize(parseResult.getFileSize())
                .fileType(parseResult.getFileType())
                .textContent(parseResult.getTextContent())
                .images(new ArrayList<>()) // 初始化为空列表，后续会填充
                .build();
    }

    /**
     * 上传图片到S3
     */
    private void uploadImagesToS3(List<DocumentParseUtils.ImageData> imageDataList, 
                                DocumentParseResponseDTO response, 
                                DocumentParseRequestDTO request) {
        List<DocumentParseResponseDTO.ParsedImageInfo> parsedImages = new ArrayList<>();
        
        for (DocumentParseUtils.ImageData imageData : imageDataList) {
            try {
                // 生成文件名
                String fileName = generateImageFileName(imageData, request);
                
                // 上传到S3
                String imageUrl = fileManager.uploadFile2AmazonS3(
                        imageData.getImageBytes(), 
                        fileName, 
                        getContentType(imageData.getImageFormat())
                );
                
                if (StringUtils.hasText(imageUrl)) {
                    // 使用文件名作为S3 key
                    
                    DocumentParseResponseDTO.ParsedImageInfo imageInfo = DocumentParseResponseDTO.ParsedImageInfo.builder()
                            .imageIndex(imageData.getImageIndex())
                            .imageName(imageData.getImageName())
                            .imageFormat(imageData.getImageFormat())
                            .width(imageData.getWidth())
                            .height(imageData.getHeight())
                            .imageSize(imageData.getImageSize())
                            .imageUrl(imageUrl)
                            .s3Key(fileName)
                            .uniqueId(imageData.getUniqueId())
                            .build();
                    
                    parsedImages.add(imageInfo);
                    
                    log.info("图片上传成功，索引: {}, 文件名: {}, URL: {}", 
                            imageData.getImageIndex(), fileName, imageUrl);
                } else {
                    log.warn("图片上传失败，索引: {}, 文件名: {}", imageData.getImageIndex(), fileName);
                }
                
            } catch (Exception e) {
                log.error("上传图片到S3失败，索引: {}", imageData.getImageIndex(), e);
            }
        }
        
        response.setImages(parsedImages);
    }

    /**
     * 生成图片文件名
     */
    private String generateImageFileName(DocumentParseUtils.ImageData imageData, DocumentParseRequestDTO request) {
        String fileName;
        if (Boolean.TRUE.equals(request.getGenerateUniqueName())) {
            fileName = String.format(DRAFTING_SPECIFICATION_DOCUMENT_PARSE_DOC_IMAGE + "%d_%s.%s",
                    imageData.getImageIndex(), UUID.randomUUID().toString().substring(0, 8),
                    imageData.getImageFormat());
        } else {
            fileName = String.format(DRAFTING_SPECIFICATION_DOCUMENT_PARSE_DOC_IMAGE + "%d.%s", imageData.getImageIndex(),
                    imageData.getImageFormat());
        }
        
        return fileName;
    }

    /**
     * 获取内容类型
     */
    private ContentType getContentType(String imageFormat) {
        return switch (imageFormat.toLowerCase()) {
            case "jpg", "jpeg" -> ContentType.IMAGE_JPEG;
            case "png" -> ContentType.IMAGE_PNG;
            case "gif" -> ContentType.IMAGE_GIF;
            case "bmp" -> ContentType.IMAGE_BMP;
            default -> ContentType.APPLICATION_OCTET_STREAM;
        };
    }
}
