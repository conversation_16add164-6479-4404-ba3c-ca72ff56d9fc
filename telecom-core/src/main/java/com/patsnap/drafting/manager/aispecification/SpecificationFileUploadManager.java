package com.patsnap.drafting.manager.aispecification;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.enums.aispecification.UploadContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.manager.DocumentParseManager;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.DocumentParseFigureBO;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationFileUploadBO;
import com.patsnap.drafting.request.DocumentParseRequestDTO;
import com.patsnap.drafting.request.aispecification.Figure;
import com.patsnap.drafting.response.DocumentParseResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.DOCUMENT_PARSE_FIGURES;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURES;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.SPECIFICATION_FILE_UPLOAD;
import static com.patsnap.drafting.exception.errorcode.DocumentParseErrorCodeEnum.PARSE_FAILED;

/**
 * @description 说明书文件上传
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SpecificationFileUploadManager {
    
    private final DocumentParseManager documentParseManager;
    private final FileManager fileManager;
    private final AiTaskManager aiTaskManager;
    
    public Boolean uploadFile(String taskId, MultipartFile file) {
        log.info("开始上传文件到S3，任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename());
        
        try {
            String s3Key = uploadFileToS3(file);
            // 保存文件信息到任务内容
            saveFileUploadResult(taskId, s3Key, file.getOriginalFilename(), file.getContentType(), file.getSize());
            log.info("文件上传成功，任务ID: {}, S3键: {}", taskId, s3Key);
            // 构造响应
            return true;
        } catch (IOException e) {
            log.error("读取文件内容失败，任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename(), e);
            throw new BizException(PARSE_FAILED);
        } catch (Exception e) {
            log.error("文件上传失败，任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename(), e);
            throw new BizException(PARSE_FAILED);
        }
    }
    
    private String uploadFileToS3(MultipartFile file) throws IOException {
        // 参数校验
        documentParseManager.validateFile(file);
        // 获取文件字节数组
        byte[] fileBytes = file.getBytes();
        // 生成文件名和S3 key
        String originalFilename = file.getOriginalFilename();
        String s3Key = generateS3Key(originalFilename);
        // 确定文件内容类型
        ContentType contentType = determineContentType(FilenameUtils.getExtension(originalFilename));
        // 上传到S3
        String fileUrl = fileManager.uploadFile2AmazonS3(fileBytes, s3Key, contentType);
        
        if (StringUtils.isBlank(fileUrl)) {
            log.error("文件上传到S3失败，文件名: {}", originalFilename);
            throw new BizException(PARSE_FAILED);
        }
        return s3Key;
    }
    
    public DocumentParseResponseDTO parseFile(String taskId, MultipartFile file, String contentType) {
        // 根据contentType获取解析类型
        UploadContentTypeEnum uploadContentTypeEnum = UploadContentTypeEnum.fromName(contentType);
        DocumentParseRequestDTO request = new DocumentParseRequestDTO();
        request.setParseType(uploadContentTypeEnum.getParseType());
        
        // 解析文档
        DocumentParseResponseDTO parseResult = documentParseManager.parseDocument(file, request);
        if (!"SUCCESS".equals(parseResult.getStatus())) {
            log.error("文档解析失败，文件名: {}", file.getOriginalFilename());
            throw new BizException(PARSE_FAILED);
        }
        // 保存解析结果
        saveParseResult(taskId, parseResult);
        return parseResult;
    }
    
    /**
     * 保存解析结果
     */
    private void saveParseResult(String taskId, DocumentParseResponseDTO parseResult) {
        if (CollectionUtils.isEmpty(parseResult.getImages())) {
            log.warn("没有图片数据，跳过保存图片，任务ID: {}", taskId);
            return;
        }
        log.info("保存图片数据，任务ID: {}", taskId);
        // 保存图片数据
        List<String> imageS3KeyList = parseResult.getImages().stream().map(
                DocumentParseResponseDTO.ParsedImageInfo::getS3Key).toList();
        DocumentParseFigureBO documentParseFigureBO = new DocumentParseFigureBO();
        documentParseFigureBO.setImageS3Keys(imageS3KeyList);
        aiTaskManager.updateTaskContent(taskId, DOCUMENT_PARSE_FIGURES, documentParseFigureBO);
    }
    
    public DocumentParseResponseDTO getParseImages(String taskId) {
        DocumentParseFigureBO documentParseFigureBO = aiTaskManager.getTaskContent(taskId, DOCUMENT_PARSE_FIGURES);
        List<String> imageS3KeyList = Optional.ofNullable(documentParseFigureBO).map(DocumentParseFigureBO::getImageS3Keys)
                .orElseGet(Collections::emptyList);
        List<DocumentParseResponseDTO.ParsedImageInfo> parsedImages = new ArrayList<>();
        for (String imageS3Key : imageS3KeyList) {
            parsedImages.add(getImageInfo(imageS3Key));
        }
        return DocumentParseResponseDTO.builder()
                .images(parsedImages)
                .build();
    }
    
    private DocumentParseResponseDTO.ParsedImageInfo getImageInfo(String imageS3Key) {
        String signedUrl = fileManager.signFile(imageS3Key);
        return DocumentParseResponseDTO.ParsedImageInfo.builder()
                .imageUrl(signedUrl)
                .s3Key(imageS3Key)
                .build();
    }
    
    /**
     * 生成S3存储键
     */
    private String generateS3Key(String originalFilename) {
        String userId = UserIdHolder.get();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String extension = FilenameUtils.getExtension(originalFilename);
        
        // 生成唯一文件名：规格说明书_用户ID_时间戳_原始文件名.扩展名
        String fileName = String.format("specification_%s_%s_%s.%s", userId, timestamp, baseName, extension);
        
        // S3键格式：drafting/specifications/userId/fileName
        return String.format("drafting/specifications/%s/%s", userId, fileName);
    }
    
    /**
     * 根据文件扩展名确定内容类型
     */
    private ContentType determineContentType(String fileExtension) {
        if (StringUtils.isBlank(fileExtension)) {
            return ContentType.APPLICATION_OCTET_STREAM;
        }
        
        return switch (fileExtension.toLowerCase()) {
            case "pdf" -> ContentType.create("application/pdf");
            case "doc" -> ContentType.create("application/msword");
            case "docx" ->
                    ContentType.create("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            case "txt" -> ContentType.TEXT_PLAIN;
            default -> ContentType.MULTIPART_FORM_DATA;
        };
    }
    
    /**
     * 保存文件上传结果到任务内容
     */
    private void saveFileUploadResult(String taskId, String s3Key, String originalFileName, 
                                    String contentType, long fileSize) {
        SpecificationFileUploadBO uploadBO = new SpecificationFileUploadBO();
        uploadBO.setS3Key(s3Key);
        uploadBO.setOriginalFileName(originalFileName);
        uploadBO.setContentType(contentType);
        uploadBO.setFileSize(fileSize);
        
        log.info("保存文件上传信息到任务内容，任务ID: {}, S3键: {}", taskId, s3Key);
        aiTaskManager.updateTaskContent(taskId, SPECIFICATION_FILE_UPLOAD, uploadBO);
    }
    
    /**
     * 将解析出的图片结果保存到任务内容表中，content_type=FIGURES
     *
     * @param taskId 任务ID
     * @param imageS3Keys 图片S3键列表
     * @return 保存的图片内容信息
     */
    public FigureContentBO saveParsedFigures(String taskId, List<String> imageS3Keys) {
        // 检查编辑权限
        aiTaskManager.checkEditPermission(taskId);
        
        log.info("开始保存解析图片结果，任务ID: {}, 图片数量: {}", taskId, imageS3Keys.size());
        
        // 转换为Figure列表
        List<Figure> figures = convertS3KeysToFigures(imageS3Keys);
        
        // 构建FigureContentBO
        FigureContentBO figureContentBO = FigureContentBO.builder()
                .figures(figures)
                .build();
        
        // 保存到任务内容表，content_type为FIGURES
        aiTaskManager.updateTaskContent(taskId, FIGURES, figureContentBO);
        
        log.info("解析图片结果保存成功，任务ID: {}, 图片数量: {}", taskId, figures.size());
        
        return figureContentBO;
    }
    
    /**
     * 将S3键列表转换为Figure列表
     *
     * @param imageS3Keys 图片S3键列表
     * @return Figure列表
     */
    private List<Figure> convertS3KeysToFigures(List<String> imageS3Keys) {
        List<Figure> figures = new ArrayList<>();
        
        for (String s3Key : imageS3Keys) {
            // 创建Image对象
            Figure.Image image = new Figure.Image().setS3Key(s3Key);
            
            // 创建Figure对象
            Figure figure = new Figure().setUniqueId(UUID.randomUUID().toString()).setAbstractFigure(false)
                    .setImage(image);
            
            figures.add(figure);
        }
        
        return figures;
    }
    
}
